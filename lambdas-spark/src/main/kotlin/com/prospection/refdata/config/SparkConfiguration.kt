package com.prospection.refdata.config

import com.prospection.refdata.common.SparkConfig
import org.apache.spark.sql.SparkSession

object SparkConfiguration {
    // Do not use by lazy here to utilise AWS Lambda 10s initialisation CPU bust
    val spark: SparkSession = run {
        val numCores = System.getenv("NUM_CORES")?.toInt() ?: 1
        println("Available vCPUs: ${Runtime.getRuntime().availableProcessors()}")
        println("Cores to use: $numCores")
        val sparkConfig = SparkConfig.getCloudSparkConfig()
            .set("spark.master", "local[$numCores]")
            .set("spark.sql.shuffle.partitions", numCores.toString())
            .set("spark.default.parallelism", numCores.toString())
            .set("spark.sql.adaptive.enabled", "false")
            .set("spark.shuffle.compress", "false")
            .set("spark.broadcast.compress", "false")
            .set("spark.ui.enabled", "false")
            // must set for lambda to work
            .set("spark.driver.bindAddress", "127.0.0.1")
            // Configure temp directories for read-only filesystem environments
            .set("spark.local.dir", "/tmp/spark")
            .set("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
            // Set Java temp directory to writable location
            .set("spark.driver.extraJavaOptions", "-Djava.io.tmpdir=/tmp")
            .set("spark.executor.extraJavaOptions", "-Djava.io.tmpdir=/tmp")
        val spark = SparkSession.builder()
            .appName("pd-ref-data-service-v2")
            .config(sparkConfig)
            .orCreate
        spark.sparkContext().setLogLevel("WARN")
        spark
    }
}