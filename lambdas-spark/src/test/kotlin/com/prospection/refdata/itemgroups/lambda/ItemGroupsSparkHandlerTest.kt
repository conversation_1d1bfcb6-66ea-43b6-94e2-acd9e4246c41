package com.prospection.refdata.itemgroups.lambda

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import com.prospection.refdata.itemgroups.integration.ItemGroupPreviewRequestPayload
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.io.OutputStream

class ItemGroupsSparkHandlerTest {
    private val mockObjectMapper: ObjectMapper = mock()
    private val mockItemGroupsSparkPort: ItemGroupsSparkPort = mock()

    private val itemGroupsSparkHandler = ItemGroupsSparkHandler(mockObjectMapper, mockItemGroupsSparkPort)

    @AfterEach
    fun tearDown() {
        reset(mockObjectMapper, mockItemGroupsSparkPort)
    }

    @Test
    fun `handleRequest should parse input call itemGroupsSparkPort with correct params`() {
        // Given
        val outputStream = ByteArrayOutputStream(1024);
        val inputStream = ByteArrayInputStream("a string".toByteArray())
        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        val codingSystemToClassifications = mapOf(
            "code1" to listOf(
                CodingSystemToClassification("1", "code1", "class1", "code")
            ),
            "code2" to listOf(
                CodingSystemToClassification("2", "code2", "class2", "code"),
                CodingSystemToClassification("3", "code2", "class3", "code")
            ),
        )
        val version = "2021-12-20"
        val payload = ItemGroupPreviewRequestPayload(itemGroup, codingSystemToClassifications, version)
        whenever(mockObjectMapper.readValue(inputStream, ItemGroupPreviewRequestPayload::class.java)).thenReturn(payload)
        val result = listOf(ItemGroupPreviewResult("a", 1, listOf("a", "b"), listOf(mapOf("a" to "b"))))
        whenever(mockItemGroupsSparkPort.getItemGroupPreview(itemGroup, codingSystemToClassifications, version))
            .thenReturn(result)

        // When
        itemGroupsSparkHandler.handleRequest(inputStream, outputStream, mock())

        // Then
        verify(mockObjectMapper, times(1)).readValue(any<InputStream>(), any<Class<Any>>())
        verify(mockItemGroupsSparkPort, times(1)).getItemGroupPreview(any(), any(), any())

        val outputStreamCaptor = argumentCaptor<OutputStream>()
        val resultCaptor = argumentCaptor<List<ItemGroupPreviewResult>>()
        verify(mockObjectMapper, times(1)).writeValue(outputStreamCaptor.capture(), resultCaptor.capture())
        assertSame(outputStream, outputStreamCaptor.firstValue)
        assertSame(result, resultCaptor.firstValue)
    }
}