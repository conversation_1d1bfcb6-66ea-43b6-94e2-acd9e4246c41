plugins {
    id("java-library")
    id("scala")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

dependencies {
    implementation(libs.spark.sql)
    implementation(libs.scala.logging)

    testImplementation(libs.bundles.scala.test)
    // Add ScalaTest JUnit Platform engine for proper test discovery
    testRuntimeOnly("org.scalatestplus:junit-5-10_2.13:********")
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }

    // Configure for ScalaTest
    systemProperty 'java.awt.headless', 'true'
}
