package com.prospection.refdata.etl.pbsauthority

import com.prospection.refdata.etl.common.CodingSystems.PBS_AUTHORITY
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._

class PbsAuthorityEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("pbs-authority/raw/PBS_10PRCNT_QUARTERLY_REPORT_CAVEATS_25SEP2022.xlsx").getPath
        val etlJobParams = getEtlJobParams(
            inputPaths = Map(PBS_AUTHORITY -> resourcePath),
            version = "20220925")
        val spyEtlJob = spy(new PbsAuthorityEtlJob(spark, etlJobParams))

        doNothing().when(spyEtlJob).storeInWarehouse(any())
        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
