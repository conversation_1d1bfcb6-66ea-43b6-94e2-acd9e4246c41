import com.github.jk1.license.filter.DependencyFilter
import com.github.jk1.license.filter.LicenseBundleNormalizer
import com.github.jk1.license.render.InventoryHtmlReportRenderer
import com.github.jk1.license.render.ReportRenderer
import org.gradle.api.tasks.testing.Test
import org.gradle.api.tasks.testing.TestReport
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val scalaVersion = libs.versions.scala.asProvider().get()

plugins {
    id("jacoco")
    id("scala")
    id("idea")
    id("io.spring.dependency-management") version "1.1.4"
    id("com.github.jk1.dependency-license-report") version "1.17"
    alias(libs.plugins.git.properties)
    alias(libs.plugins.sonar)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.kotlin.plugins.allopen)
    alias(libs.plugins.kotlin.plugins.jpa)
    alias(libs.plugins.kotlin.plugins.spring)
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

allprojects {
    group = "com.prospection"
    version = "0.0.1-SNAPSHOT"

    repositories {
        mavenCentral()
        maven {
            name = "nexus"
            url = uri("https://maven-internal.prospection.com.au/repository/prospection-private")
            credentials {
                username = findProperty("nexusUser") as String
                password = findProperty("nexusPassword") as String
            }
        }
    }

    // Gradle 8+ compatible test report aggregation
    if (project.name != "common") {
        tasks.register("mergeJUnitReports", TestReport::class) {
            group = "verification"
            description = "Merges JUnit test reports from all subprojects (Gradle 8+ compatible - much simpler!)"

            destinationDirectory = layout.buildDirectory.dir("reports/tests/merged")

            // Automatically collect test results from all projects - Gradle does the heavy lifting!
            testResults.from(rootProject.allprojects.map { proj ->
                proj.tasks.withType<Test>()
            })

            // Ensure this runs after all test tasks
            mustRunAfter(rootProject.allprojects.map { proj ->
                proj.tasks.withType<Test>()
            })
        }
    }
}

configurations.all {
    // Exclude conflicting logging implementations to use Spring Boot's Logback
    exclude(group = "org.slf4j", module = "slf4j-log4j12")
    exclude(group = "org.slf4j", module = "slf4j-reload4j")
    exclude(group = "org.slf4j", module = "slf4j-nop")
    exclude(group = "org.slf4j", module = "slf4j-simple")
    exclude(group = "org.slf4j", module = "slf4j-jdk14")
    // Exclude Log4j SLF4J implementations to avoid conflicts with Spring Boot's Logback
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j2-impl")
    exclude(group = "org.apache.logging.log4j", module = "log4j-slf4j-impl")
    // Exclude log4j-to-slf4j as it conflicts with log4j-slf4j2-impl
    exclude(group = "org.apache.logging.log4j", module = "log4j-to-slf4j")
    // Exclude commons-logging to prevent conflicts
    exclude(group = "commons-logging", module = "commons-logging")

    // CRITICAL: Exclude the AWS SDK bundle JAR that contains conflicting SLF4J implementations
    // This bundle JAR contains both shaded and unshaded SLF4J classes causing the NOPLoggerFactory issue
    exclude(group = "software.amazon.awssdk", module = "bundle")

    resolutionStrategy {
        force(libs.slf4j.api)
        // Force Logback as the SLF4J implementation
        force("ch.qos.logback:logback-classic:1.4.14")
        force("ch.qos.logback:logback-core:1.4.14")

        // Force all AWS SDK v2 components to use the same version (excluding bundle)
        eachDependency {
            if (requested.group == "software.amazon.awssdk" && requested.name != "bundle") {
                useVersion(libs.versions.aws.sdk.v2.get())
                because("Force consistent AWS SDK v2 version to avoid NoSuchMethodError")
            }
        }

        // Force all Hadoop components to use the same version
        eachDependency {
            if (requested.group == "org.apache.hadoop") {
                useVersion(libs.versions.hadoop.get())
                because("Force consistent Hadoop version to avoid NoSuchMethodError")
            }
        }
    }
}

dependencyManagement {
    dependencies {
        dependencySet("org.scala-lang:${libs.versions.scala}") {
            entry("scala-library")
        }
    }

    imports {
        mavenBom("org.springframework.boot:spring-boot-dependencies:${libs.versions.spring.boot.get()}")
    }
}

// Configure KAPT for MapStruct
kapt {
    mapDiagnosticLocations = true
    arguments {
        arg("mapstruct.defaultComponentModel", "spring")
        arg("mapstruct.unmappedTargetPolicy", "IGNORE")
        arg("mapstruct.defaultInjectionStrategy", "constructor")
        // Enable verbose logging for MapStruct
        arg("mapstruct.verbose", "true")
    }
    // This ensures that the generated code is visible to the IDE
    includeCompileClasspath = false
    // Generate stubs for better IDE support
    useBuildCache = true
    correctErrorTypes = true
}

// Configure source sets for generated code
sourceSets {
    main {
        java {
            srcDirs(
                layout.buildDirectory.dir("generated/source/kapt/main"),
                layout.buildDirectory.dir("generated/source/kaptKotlin/main")
            )
        }
        kotlin {
            srcDirs(
                layout.buildDirectory.dir("generated/source/kapt/main"),
                layout.buildDirectory.dir("generated/source/kaptKotlin/main")
            )
        }
    }
    test {
        java {
            srcDirs(
                layout.buildDirectory.dir("generated/source/kapt/test"),
                layout.buildDirectory.dir("generated/source/kaptKotlin/test"),
                "src/test/kotlin"
            )
        }
        kotlin {
            srcDirs(
                layout.buildDirectory.dir("generated/source/kapt/test"),
                layout.buildDirectory.dir("generated/source/kaptKotlin/test"),
                "src/test/kotlin"
            )
        }
    }
}

// Configure Kotlin compilation
tasks.withType<KotlinCompile> {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}

// Fix KAPT task dependencies to prevent build issues
// Create a task to clean KAPT generated files when KAPT is disabled
tasks.register("cleanKaptGeneratedFiles") {
    doLast {
        val kaptMainDir = file("build/generated/source/kapt/main")
        val kaptTestDir = file("build/generated/source/kapt/test")

        if (kaptMainDir.exists()) {
            logger.info("Cleaning KAPT main generated files")
            kaptMainDir.deleteRecursively()
        }

        if (kaptTestDir.exists()) {
            logger.info("Cleaning KAPT test generated files")
            kaptTestDir.deleteRecursively()
        }
    }
}

// Make KAPT tasks conditional on their dependencies being enabled
tasks.named("compileJava") {
    dependsOn("kaptKotlin")
    doFirst {
        // Clean KAPT generated files if KAPT tasks are disabled
        val kaptKotlinTask = tasks.findByName("kaptKotlin")
        if (kaptKotlinTask?.enabled == false) {
            tasks.getByName("cleanKaptGeneratedFiles").actions.forEach { it.execute(tasks.getByName("cleanKaptGeneratedFiles")) }
        }
    }
}

tasks.named("compileTestJava") {
    dependsOn("kaptTestKotlin")
    doFirst {
        // Clean KAPT test generated files if KAPT test tasks are disabled
        val kaptTestKotlinTask = tasks.findByName("kaptTestKotlin")
        if (kaptTestKotlinTask?.enabled == false) {
            tasks.getByName("cleanKaptGeneratedFiles").actions.forEach { it.execute(tasks.getByName("cleanKaptGeneratedFiles")) }
        }
    }
}

// Fix KAPT test stub generation dependencies using afterEvaluate to ensure tasks exist
afterEvaluate {
    tasks.findByName("kaptGenerateStubsTestKotlin")?.dependsOn("compileKotlin")
    tasks.findByName("kaptTestKotlin")?.dependsOn("compileKotlin")
}

// Make KAPT tasks conditional based on task graph
gradle.taskGraph.whenReady {
    val compileKotlinInGraph = hasTask(":compileKotlin")
    val compileTestKotlinInGraph = hasTask(":compileTestKotlin")

    // Disable KAPT tasks if their corresponding Kotlin compilation tasks are not in the graph
    if (!compileKotlinInGraph) {
        tasks.findByName("kaptKotlin")?.enabled = false
        tasks.findByName("kaptGenerateStubsKotlin")?.enabled = false
    }

    if (!compileTestKotlinInGraph) {
        tasks.findByName("kaptTestKotlin")?.enabled = false
        tasks.findByName("kaptGenerateStubsTestKotlin")?.enabled = false
    }
}

dependencies {
    // Kotlin and Spring Boot
    implementation(project("common"))
    implementation(project("scala-spark"))
    implementation(project("kotlin-spark"))

    implementation(libs.bundles.pd.boot.starters)

    // Explicitly include Spring Boot's logging starter to ensure Logback is available
    implementation("org.springframework.boot:spring-boot-starter-logging")

    // Add logstash encoder for JSON logging in production
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")

    // Add Jakarta Servlet API 5.0.0 for Spark UI compatibility
    implementation("jakarta.servlet:jakarta.servlet-api:5.0.0")

    // AWS Glue and Lambda dependencies - upgraded to AWS SDK v2
    // Bundle JAR is excluded globally to prevent SLF4J conflicts
    implementation(libs.aws.sdk.sns)
    implementation(libs.aws.sdk.glue)
    implementation(libs.aws.sdk.lambda)
    implementation(libs.aws.sdk.apache.client)
    implementation(libs.aws.sdk.s3)
    implementation(libs.aws.sdk.core)
    implementation(libs.aws.sdk.s3.transfer.manager)

    // Additional Spark-related libraries
    implementation(libs.spark.sql)
    implementation(libs.spark.excel)
    implementation(libs.hadoop.aws)
    implementation(libs.kotlin.spark.api)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.kotlinx.serialization.core)
    implementation("org.apache.poi:poi:5.2.5")
    implementation("org.apache.poi:poi-ooxml:5.2.5")

    // Other dependencies
    implementation(libs.kotlinReflect)
    implementation(libs.kotlinStdlib)
    implementation(libs.kotlinStdlibJdk8)
    implementation(libs.slf4j.api)
    implementation(libs.jackson.module.kotlin)
    implementation(libs.liquibase.core)
    implementation(libs.springdoc.openapi.starter.webmvc.ui)
    implementation(libs.jakarta.validation.api)
    implementation(libs.jakarta.persistence.api)
    implementation(libs.unleash)
    implementation(libs.awspring.cloud.starter.sqs)
    implementation(libs.jooq)
    implementation(libs.univocity.parsers)
    implementation(libs.hypersistence.utils)

    // MapStruct - Must be before Lombok
    implementation(libs.mapstruct)
    kapt(libs.mapstruct.processor)
    kapt(libs.mapstruct.kotlin)

    // Lombok
    compileOnly(libs.lombok)
    annotationProcessor(libs.lombok)
    kapt(libs.lombok)

    // Ensure Lombok doesn't process MapStruct annotations
    kapt(libs.lombok.mapstruct.binding)

    // Ensure MapStruct works with Kotlin
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")

    testImplementation(platform("software.amazon.awssdk:bom:${libs.versions.aws.sdk.v2.get()}"))
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.inline)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.assertj.core)
    testImplementation(libs.spring.security.test)
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(group = "com.vaadin.external.google", module = "android-json")
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }

    // Add Testcontainers dependencies
    testImplementation(platform("org.testcontainers:testcontainers-bom:${libs.versions.testcontainers.get()}"))
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("org.testcontainers:localstack")
}

jacoco {
    toolVersion = "0.8.7"
}

allOpen {
    annotation("jakarta.persistence.Entity")
    annotation("jakarta.persistence.Embeddable")
    annotation("jakarta.persistence.MappedSuperclass")
}

sonarqube {
    properties {
        property("sonar.host.url", "https://sonarcloud.io/")
        property("sonar.organization", "teamprospection")

        property("sonar.projectKey", "teamprospection_pd-ref-data-service")
        property("sonar.projectName", "pd-ref-data-service")

        property("sonar.sourceEncoding", "UTF-8")
        property("sonar.sources", "${project.projectDir}/src/main/")
        property("sonar.tests", "${project.projectDir}/src/test/")
        property("sonar.java.codeCoveragePlugin", "jacoco")
        property("sonar.coverage.jacoco.xmlReportPaths", "${project.layout.buildDirectory.get()}/reports/jacoco/test/jacocoTestReport.xml")
        property("sonar.junit.reportPaths", "${project.layout.buildDirectory.get()}/test-results/test")
    }
}

licenseReport {
    renderers = arrayOf<ReportRenderer>(InventoryHtmlReportRenderer("report.html", "Backend"))
    filters = arrayOf<DependencyFilter>(LicenseBundleNormalizer())
}



tasks.getByName<Jar>("jar") {
    enabled = false
}

tasks.withType<Test> {
    useJUnitPlatform()
    finalizedBy(tasks.jacocoTestReport)

    // Add JVM arguments for Spark 4.0.0 compatibility with JDK 17
    jvmArgs(
        "--add-opens=java.base/java.lang=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
        "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
        "--add-opens=java.base/java.io=ALL-UNNAMED",
        "--add-opens=java.base/java.net=ALL-UNNAMED",
        "--add-opens=java.base/java.nio=ALL-UNNAMED",
        "--add-opens=java.base/java.util=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
        "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
        "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
        "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
        "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
        "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
    )
}

tasks.withType<ScalaCompile> {
    scalaCompileOptions.forkOptions.jvmArgs = listOf("-Dzinc.scala.version=$scalaVersion")
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    reports {
        xml.required.set(true)
    }
}

// Configure duplicate file handling
tasks.withType<AbstractArchiveTask>().configureEach {
    isPreserveFileTimestamps = false
    isReproducibleFileOrder = true
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
